<template>
  <div class="price-screen-container">
    <!-- 左侧价格表格区域 (60%) -->
    <div class="table-section">
      <div class="table-header">
        <h2>商品价格信息</h2>
      </div>
      <div class="table-wrapper" v-loading="loading">
        <div class="scrolling-table" ref="scrollingTable">
          <el-table :data="priceData" stripe :show-header="true" height="100%" class="price-table">
            <el-table-column prop="code" label="编码" min-width="120" align="center" />
            <el-table-column prop="name" label="名称" min-width="200" align="center" />
            <el-table-column prop="spec" label="规格" min-width="150" align="center" />
            <el-table-column prop="unit" label="单位" min-width="80" align="center" />
            <el-table-column prop="type" label="类型" min-width="120" align="center" />
            <el-table-column prop="price" label="单价(元)" min-width="120" align="center">
              <template slot-scope="scope">
                <span class="price-text">¥{{ scope.row.price }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- 右侧视频区域 (40%) -->
    <div class="video-section">
      <div class="video-wrapper">
        <video
          ref="priceVideo"
          class="price-video"
          :src="videoSrc"
          autoplay
          loop
          muted
          @error="handleVideoError"
          @loadstart="handleVideoLoadStart"
          @canplay="handleVideoCanPlay"
        >
          您的浏览器不支持视频播放
        </video>
        <div v-if="videoError" class="video-error">
          <i class="el-icon-video-camera-solid"></i>
          <p>视频加载失败</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import priceTestData from "./priceTestData.js";

export default {
  name: "priceScreen",
  data() {
    return {
      loading: true,
      videoError: false,
      videoSrc: require("@/assets/videos/price_screen_video.mp4"),
      scrollTimer: null,
      scrollSpeed: 0.15, // 滚动速度（像素/帧）
      currentScrollTop: 0, // 当前滚动位置

      // 从外部文件导入商品价格数据
      priceData: priceTestData,
    };
  },

  computed: {},
  created() {
    // this.testFun();
  },

  mounted() {
    this.initPage();
  },

  methods: {
    async testFun() {
      const res = await this.$api.price.queryPrice();
      if (res.success) {
        // const res = {
        //   success: true,
        //   data: {
        //     ResultCode: "0",
        //     ErrorMsg: "成功",
        //     SerialNo: "2",
        //     ItemDesc: "美容义齿加收(特殊设计)",
        //     ItemCode: "310518004-1",
        //     PricesNo: "",
        //     Uom: "每牙",
        //     Price: "18",
        //     SpecialPrice: "",
        //     Factory: "",
        //     ContentDesc: "",
        //     ChargeStandard: "",
        //     InsureSign: "",
        //     RegistrationNo: "",
        //     RegExpDate: "",
        //     SpecInfo: "",
        //     TarSubCate: "其他",
        //   },
        //   message: "物价查询成功",
        // };
        console.log("queryPrice查", res);
      } else {
        console.log("queryPrice查2", res);
        this.$message.error(res.message);
      }
    },

    // 初始化页面
    async initPage() {
      // 模拟数据加载
      setTimeout(() => {
        this.loading = false;
        this.$nextTick(() => {
          // 延迟一点时间确保表格完全渲染
          setTimeout(() => {
            this.startScrolling();
          }, 500);
        });
      }, 1000);
    },

    // 开始滚动动画
    startScrolling() {
      const tableWrapper = this.$refs.scrollingTable;
      if (!tableWrapper) {
        console.log("scrollingTable ref not found");
        return;
      }

      // 等待DOM渲染完成后再开始滚动
      this.$nextTick(() => {
        // 查找表格的body容器
        const tableBody = tableWrapper.querySelector(".el-table__body-wrapper");
        if (!tableBody) {
          console.log("table body wrapper not found");
          return;
        }

        console.log("Starting scroll animation");
        const scroll = () => {
          this.currentScrollTop += this.scrollSpeed;

          // 获取表格的实际高度
          const scrollHeight = tableBody.scrollHeight;
          const clientHeight = tableBody.clientHeight;
          const maxScroll = scrollHeight - clientHeight;

          // 当滚动到一半时（即原始数据的底部），重置到顶部实现无缝循环
          if (this.currentScrollTop >= maxScroll) {
            this.currentScrollTop = 0;
          }

          tableBody.scrollTop = this.currentScrollTop;
          this.scrollTimer = requestAnimationFrame(scroll);
        };

        this.scrollTimer = requestAnimationFrame(scroll);
      });
    },

    // 清除滚动定时器
    clearScrollTimer() {
      if (this.scrollTimer) {
        cancelAnimationFrame(this.scrollTimer);
        this.scrollTimer = null;
      }
    },

    // 视频错误处理
    handleVideoError() {
      this.videoError = true;
      console.error("视频加载失败");
    },

    // 视频开始加载
    handleVideoLoadStart() {
      this.videoError = false;
    },

    // 视频可以播放
    handleVideoCanPlay() {
      this.videoError = false;
    },
  },
  beforeDestroy() {
    this.clearScrollTimer();
  },
};
</script>

<style lang="scss" scoped>
.price-screen-container {
  display: flex;
  width: 100%;
  height: 100vh;
  background: #f5f5f5;
  overflow: hidden;

  // 左侧表格区域 (60%)
  .table-section {
    width: 60%;
    height: 100%;
    padding: 15px;
    display: flex;
    flex-direction: column;

    .table-header {
      margin-bottom: 15px;
      text-align: center;

      h2 {
        color: #333;
        font-size: 24px;
        font-weight: bold;
        margin: 0;
      }
    }

    .table-wrapper {
      flex: 1;
      background: #ffffff;
      border: 1px solid #e0e0e0;
      overflow: hidden;
      position: relative;

      .scrolling-table {
        height: 100%;
        overflow: hidden;

        .price-table {
          width: 100%;
          height: 100%;

          // 表格头部样式
          ::v-deep .el-table__header-wrapper {
            .el-table__header {
              th {
                background: #409eff;
                color: #ffffff;
                font-weight: bold;
                font-size: 14px;
                text-align: center;
                border-bottom: 1px solid #ddd;

                .cell {
                  padding: 12px 8px;
                }
              }
            }
          }

          // 表格主体样式
          ::v-deep .el-table__body-wrapper {
            .el-table__body {
              tr {
                td {
                  border-bottom: 1px solid #f0f0f0;
                  font-size: 13px;
                  color: #333;

                  .cell {
                    padding: 10px 8px;
                    text-align: center;
                  }

                  .price-text {
                    font-weight: bold;
                    color: #e74c3c;
                    font-size: 14px;
                  }
                }

                // 斑马纹样式
                &.el-table__row--striped {
                  td {
                    background-color: #fafafa;
                  }
                }
              }
            }
          }

          // 移除表格外边框
          ::v-deep .el-table {
            border: none;

            &::before {
              display: none;
            }

            &::after {
              display: none;
            }
          }
        }
      }
    }
  }

  // 右侧视频区域 (40%)
  .video-section {
    width: 40%;
    height: 100%;
    padding: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #000;

    .video-wrapper {
      width: 100%;
      height: 100%;
      position: relative;
      background: #000;

      .price-video {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .video-error {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.8);
        color: #ffffff;

        i {
          font-size: 48px;
          margin-bottom: 16px;
        }

        p {
          font-size: 16px;
          margin: 0;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .price-screen-container {
    .table-section {
      .table-header {
        h2 {
          font-size: 20px;
        }
      }

      .table-wrapper {
        .scrolling-table {
          .price-table {
            ::v-deep .el-table__header-wrapper {
              .el-table__header {
                th {
                  font-size: 13px;

                  .cell {
                    padding: 10px 6px;
                  }
                }
              }
            }

            ::v-deep .el-table__body-wrapper {
              .el-table__body {
                tr {
                  td {
                    font-size: 12px;

                    .cell {
                      padding: 8px 6px;
                    }

                    .price-text {
                      font-size: 13px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .price-screen-container {
    flex-direction: column;

    .table-section {
      width: 100%;
      height: 70%;
      padding: 10px;
    }

    .video-section {
      width: 100%;
      height: 30%;
      padding: 10px;

      .video-wrapper {
        height: 100%;
      }
    }
  }
}

// Loading 样式覆盖
::v-deep .el-loading-mask {
  background-color: rgba(255, 255, 255, 0.9);

  .el-loading-spinner {
    .el-loading-text {
      color: #409eff;
      font-weight: bold;
    }

    .circular {
      color: #409eff;
    }
  }
}
</style>
