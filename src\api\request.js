import axios from 'axios'
import { API_CONFIG, SOAP_CONFIG} from './config.js'

// 创建axios实例
const soapClient = axios.create({
  baseURL: API_CONFIG.baseURL,
  timeout: API_CONFIG.timeout,
  headers: SOAP_CONFIG.headers,
  // 根据环境配置SSL证书验证
  httpsAgent: new (require('https').Agent)({
    rejectUnauthorized: API_CONFIG.rejectUnauthorized
  })
})

/**
 * 构建SOAP XML请求体
 * @param {string} action - 操作类型 (如: MES0061)
 * @param {string} message - 请求消息内容 (XML格式)
 * @returns {string} 完整的SOAP XML字符串
 */
function buildSoapXML(action, message) {
  // 修复：移除XML声明，与Postman保持一致
  return `<soap:Envelope xmlns:soap="${SOAP_CONFIG.envelope.soap}">
    <soap:Body>
        <HIPMessageServer xmlns="${SOAP_CONFIG.namespace}">
            <action>${action}</action>
            <message>
                <![CDATA[${message}]]>
            </message>
        </HIPMessageServer>
    </soap:Body>
</soap:Envelope>`
}

/**
 * 解析SOAP响应，提取实际数据
 * @param {string} soapResponse - SOAP XML响应字符串
 * @returns {Object} 解析后的JSON对象
 */
function parseSoapResponse(soapResponse) {
  try {
    // 使用正则表达式提取CDATA中的内容
    const cdataMatch = soapResponse.match(/<!\[CDATA\[(.*?)\]\]>/s)
    if (cdataMatch && cdataMatch[1]) {
      const xmlContent = cdataMatch[1].trim()
      
      // 如果内容是XML格式，尝试解析为JSON
      if (xmlContent.startsWith('<')) {
        return parseXMLToJSON(xmlContent)
      }
      
      // 如果已经是JSON格式，直接解析
      try {
        return JSON.parse(xmlContent)
      } catch (e) {
        return { data: xmlContent }
      }
    }
    
    // 如果没有找到CDATA，返回原始响应
    return { rawResponse: soapResponse }
  } catch (error) {
    console.error('解析SOAP响应失败:', error)
    return { error: '响应解析失败', rawResponse: soapResponse }
  }
}

/**
 * 简单的XML转JSON解析器
 * @param {string} xml - XML字符串
 * @returns {Object} JSON对象
 */
function parseXMLToJSON(xml) {
  const result = {}
  
  // 简单的XML解析逻辑（可根据实际需要扩展）
  const tagRegex = /<(\w+)>([^<]*)<\/\1>/g
  let match
  
  while ((match = tagRegex.exec(xml)) !== null) {
    const [, tagName, content] = match
    result[tagName] = content.trim()
  }
  
  return Object.keys(result).length > 0 ? result : { data: xml }
}

/**
 * 发送SOAP请求的通用方法
 * @param {string} action - 操作类型
 * @param {string} message - 请求消息
 * @param {Object} options - 额外配置选项
 * @returns {Promise<Object>} 返回解析后的响应数据
 */
export async function sendSoapRequest(action, message, options = {}) {
  try {
    console.log(`发送SOAP请求 - Action: ${action}`)
    console.log('请求消息:', message)
    
    // 构建SOAP XML
    const soapXML = buildSoapXML(action, message).trim();
    
    // 发送请求
    const response = await soapClient.post('', soapXML, {
      ...options,
      headers: {
        ...SOAP_CONFIG.headers,
        ...options.headers
      }
    })

    console.log('SOAP响应状态:', response.status)
    console.log('SOAP响应数据:', response.data)
    
    // 解析响应
    const parsedData = parseSoapResponse(response.data)
    
    return {
      success: true,
      data: parsedData,
      status: response.status,
      headers: response.headers
    }
    
  } catch (error) {
    console.error('SOAP请求失败:', error)
    
    // 处理不同类型的错误
    if (error.response) {
      // 服务器响应了错误状态码
      return {
        success: false,
        error: '服务器响应错误',
        status: error.response.status,
        data: error.response.data,
        message: error.message
      }
    } else if (error.request) {
      // 请求已发出但没有收到响应
      return {
        success: false,
        error: '网络连接错误',
        message: '无法连接到服务器，请检查网络连接'
      }
    } else {
      // 其他错误
      return {
        success: false,
        error: '请求配置错误',
        message: error.message
      }
    }
  }
}

/**
 * 请求拦截器 - 可用于添加通用的请求处理逻辑
 */
soapClient.interceptors.request.use(
  config => {
    console.log('发送SOAP请求:', config.url)
    return config
  },
  error => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

/**
 * 响应拦截器 - 可用于添加通用的响应处理逻辑
 */
soapClient.interceptors.response.use(
  response => {
    console.log('收到SOAP响应:', response.status)
    return response
  },
  error => {
    console.error('响应拦截器错误:', error)
    return Promise.reject(error)
  }
)

export default soapClient
